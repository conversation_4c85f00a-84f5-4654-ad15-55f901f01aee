#!/usr/bin/env python3
"""
Environment check script for YOLOv8 training setup
"""

def check_environment():
    print("=== Environment Check ===")
    
    # Check Python version
    import sys
    print(f"Python version: {sys.version}")
    
    # Check PyTorch
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU count: {torch.cuda.device_count()}")
            print(f"GPU name: {torch.cuda.get_device_name(0)}")
        else:
            print("No CUDA GPU detected")
    except ImportError:
        print("PyTorch not installed")
    
    # Check Ultralytics
    try:
        import ultralytics
        print(f"Ultralytics version: {ultralytics.__version__}")
    except ImportError:
        print("Ultralytics not installed")
    
    # Check TensorFlow
    try:
        import tensorflow as tf
        print(f"TensorFlow version: {tf.__version__}")
    except ImportError:
        print("TensorFlow not installed")
    
    print("=== End Environment Check ===")

if __name__ == "__main__":
    check_environment()
